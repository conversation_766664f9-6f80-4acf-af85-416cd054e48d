<?php

namespace App\Filament\App\Resources\PurchaseDocResource\Forms;

use App\Enums\DocumentTypes;
use App\Enums\PaymentTypes;
use App\Enums\TradeDocVatMethod;
use App\Filament\App\Resources\PartnerResource;
use App\Models\Partner;
use App\Models\PurchaseDoc;
use App\Repositories\CurrenciesRepository;
use App\Repositories\CurrencyRatesExchangeRepository;
use App\Repositories\DocumentSeriesRepository;
use App\Repositories\PartnersRepository;
use Filament\Actions\Action as HeaderAction;
use Filament\Forms;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Infolists\Components\Actions\Action as InfoListAction;
use Filament\Support\Colors\Color;
use Filament\Tables\Actions\Action as TableAction;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

class PurchaseDocForm
{
    public static function editForm(Form $form): Form
    {

        return $form
            ->schema([
                ...self::baseSchema($form)
            ])
            ->inlineLabel(true)
            ->columns([
                'sm' => 1,
                'md' => 2,
                'lg' => 3,
            ]);
    }

    public static function createForm(Form $form): Form
    {

        return $form
            ->schema([
                self::docTypeSection(),
                ...self::baseSchema($form)
            ])
            ->inlineLabel(true)
            ->columns([
                'sm' => 1,
                'md' => 2,
                'lg' => 3,
            ]);
    }


    public static function baseSchema(Form $form): array
    {
        return [
            Forms\Components\Grid::make(2)
                ->schema([
                    self::sellerSection()
                        ->extraAttributes(['class' => 'h-full'], true)
                        ->columnSpan(1),
                    self::datesSection(),
                ]),
            self::correctionSection(),
            self::bankSection(),
            self::optionsSection(),
        ];
    }


    public static function docTypeSection(): Grid
    {
        return Forms\Components\Grid::make([
            'sm' => 1,
            'md' => 2,
            'lg' => 3,
        ])
            ->columnSpanFull()
            ->schema([
                Forms\Components\Fieldset::make('Typ dokumentu')
                    ->extraAttributes(['class' => 'accent-color-custom'], true)
                    ->columns(3)
                    ->schema([
                        Select::make('type')
                            ->columnSpan(1)
                            ->label(__('app.purchase_docs.create.type'))
                            ->options(
                                DocumentSeriesRepository::getPDocForCreateSelect()
                            )
                            ->live()
                            ->afterStateUpdated(fn($state, $livewire) => $livewire->dispatch('type-updated', $state))
                            ->required(),
                        TextInput::make('full_doc_number')
                            ->label(__('app.purchase_docs.create.full_doc_number'))
                            ->required()
                    ])
            ]);
    }


    public static function getDefaultPDD(Get $get): string
    {
        return Carbon::make($get('issued_at'))
            ?->addDays(7)
            ->format('Y-m-d');
    }

    public static function findCurrencyExchangeRate(Get $get, Set $set): void
    {
        if ($get('currency') === 'PLN') {
            $set('exchange_rate', 1);
            return;
        }
        $target = $get('currency');
        $src = 'PLN';
        $date = $get('currency_rate_date');
        if (blank($date)) {
            $date = (new Carbon($get('issued_at')))->subDay()->format('Y-m-d');
            $set('currency_rate_date', $date);
        }
        [$rate, $edate] = CurrencyRatesExchangeRepository::getNearestRate($src, $target, $date);

        $set(
            'exchange_rate',
            $rate ?? 1
        );
        $set('currency_rate_date', $edate);
    }

    public static function setPaymentDueDate(Get $get, $state): string
    {
        return Carbon::make($get('issued_at'))
            ?->endOfDay()
            ->addDays($state)->format('Y-m-d');
    }

    public static function setPaymentCreditDays(Get $get, $state): string
    {
        return Carbon::make($state)
            ?->diffInDays(Carbon::make($get('issued_at'))->startOfDay());
    }


    public static function optionsSection()
    {
        return Forms\Components\Section::make('Opcje')
            ->extraAttributes(['class' => 'h-full'], true)
            ->columns(2)
            ->collapsed(fn($operation) => $operation === 'create')
            ->collapsible(true)
            ->schema([
                Forms\Components\Grid::make(1)
                    ->columnSpan(1)
                    ->columns(1)
                    ->schema([
                        Forms\Components\Checkbox::make('meta.options.reverse_charge')
                            ->default(false)
                            ->inlineLabel(false)
                            ->hidden(
                                fn(Get $get) => (int)$get('type') === DocumentTypes::FZUP->value
                            )
                            ->formatStateUsing(
                                fn(?PurchaseDoc $record) => $record?->getMeta()->getOption('reverse_charge') ?? false
                            )
                            ->columnSpan(1)
                            ->label(__('app.trade_docs._.reverse_charge')),
                        Forms\Components\Checkbox::make('meta.options.mpp')
                            ->default(false)
                            ->inlineLabel(false)
                            ->formatStateUsing(
                                fn(?PurchaseDoc $record) => $record?->getMeta()->getOption('mpp') ?? false
                            )
                            ->hidden(
                                fn(Get $get) => (int)$get('type') === DocumentTypes::FZUP->value
                            )
                            ->columnSpan(1)
                            ->label(__('app.trade_docs._.mpp')),
                        Forms\Components\Checkbox::make('is_final_invoice')
                            ->default(false)
                            ->inlineLabel(false)
                            ->live()
                            ->visible(
                                fn(Get $get) => (int)$get('type') === DocumentTypes::FZV->value
                            )
                            ->columnSpan(1)
                            ->helperText(fn(Get $get) => match (blank($get('seller_id'))) {
                                true => 'Wybierz najpierw sprzedawcę',
                                default => null,
                            })
                            ->label(__('app.trade_docs._.final_invoice')),
                        Select::make('prepaidInvoices')
                            ->options(
                                fn(Get $get, $operation, ?Model $record) => match (blank($get('seller_id'))) {
                                    true => [],
                                    default => PurchaseDoc::where('type', DocumentTypes::FZP)
                                        ->where('seller_id', $get('seller_id'))
                                        ->when(
                                            $operation === 'create',
                                            fn($query) => $query->whereNull('final_invoice_purchase_doc_id'),
                                            fn($query) => $query->where(function ($query) use ($record) {
                                                $query->where('final_invoice_purchase_doc_id', null)
                                                    ->orWhereIn('uuid', $record->prepaidInvoices->pluck('uuid'));
                                            })
                                        )
                                        ->pluck('full_doc_number', 'uuid')
                                        ->toArray()
                                }
                            )
                            ->multiple()
                            ->preload()
                            ->requiredIf(
                                'is_final_invoice',
                                true
                            )
                            ->helperText(
                                function ($operation) {
                                    if ($operation === 'edit') {
                                        return 'Po zmianie listy faktur zaliczkowych ' .
                                            'pamiętaj o zmianie pozycji na fakturze!!!';
                                    }

                                    return '';
                                }
                            )
                            ->formatStateUsing(
                                fn(?Model $record) => $record?->prepaidInvoices->pluck('uuid')->toArray() ?? []
                            )
                            ->label(__('app.trade_docs._.prepaid_invoices'))
                            ->visible(
                                function (Get $get) {
                                    return $get('is_final_invoice') && filled($get('seller_id'));
                                }
                            ),
                        Forms\Components\Checkbox::make('final_invoice_trade_doc_id')
                            ->visible(
                                fn(Get $get) => (int)$get('type') === DocumentTypes::FZP->value
                            )
                            ->disabled(true)
                            ->columnSpan(1)
                            ->label(
                                function (?PurchaseDoc $record) {
                                    return __('app.trade_docs._.final_invoice')
                                        . match ($record?->finalInvoice ?? null) {
                                            null => ' BRAK',
                                            default => ' ' . $record?->finalInvoice->full_doc_number
                                        };
                                }
                            ),
                    ]),
                Forms\Components\Grid::make(1)
                    ->columnSpan(1)
                    ->schema([
                        Select::make('vat_method')
                            ->label(__('app.trade_docs.create.vat_method'))
                            ->options(TradeDocVatMethod::toArrayWithLabels())
                            ->hidden(
                                fn(Get $get) => (int)$get('type') === DocumentTypes::FZUP->value
                            )
                            ->required()
                            ->default(1),
                        Forms\Components\Textarea::make('meta.note')
                            ->label(__('app.trade_docs._.notes'))
                            ->maxLength(255)
                            ->formatStateUsing(
                                fn(?PurchaseDoc $record) => $record?->getMeta()->getNote() ?? ''
                            )
                            ->columnSpanFull()
                            ->default(null),
                    ]),
            ]);
    }


    public static function datesSection()
    {
        return Fieldset::make('Daty')
            ->columns(1)
            ->columnSpan(1)
            ->extraAttributes(['class' => 'h-full'], true)
            ->schema([
                DatePicker::make('issued_at')
                    ->label(__('app.trade_docs.create.issued_at'))
                    ->displayFormat('Y-m-d')
                    ->time(false)
                    ->format('Y-m-d')
                    ->native(false)
                    ->closeOnDateSelection()
                    ->live()
                    ->afterStateUpdated(
                        function (Set $set, Get $get, $state) {
                            $set(
                                'payment_due_date',
                                self::setPaymentDueDate($get, $get('payment_credit_days'))
                            );
                            $date = (new Carbon($state))->subDay()->format('Y-m-d');
                            $set('currency_rate_date', $date);
                            self::findCurrencyExchangeRate($get, $set);
                        }
                    )
                    ->default(now('Europe/Warsaw')->format('Y-m-d'))
                    ->required(),
                DatePicker::make('sells_date')
                    ->label(__('app.trade_docs.create.sells_date'))
                    ->displayFormat('Y-m-d')
                    ->native(false)
                    ->closeOnDateSelection()
                    ->default(now('Europe/Warsaw')->format('Y-m-d'))
                    ->required(),
                DatePicker::make('payment_due_date')
                    ->label(__('app.trade_docs.create.payment_due_date'))
                    ->native(false)
                    ->displayFormat('Y-m-d')
                    ->live()
                    ->closeOnDateSelection()
                    ->default(
                        fn(Get $get) => self::getDefaultPDD($get)
                    )
                    ->afterStateUpdated(
                        function (Set $set, Get $get, $state) {
                            $set('payment_credit_days', self::setPaymentCreditDays($get, $state));
                        }
                    )
                    ->required(),
                TextInput::make('payment_date')
                    ->label(__('app.trade_docs.create.is_paid'))
                    ->visible(fn($operation) => $operation === 'edit')
                    ->readOnly(true)
                    ->suffixAction(
                        self::getFormPaymentAction()->after(
                            function (Set $set, $record) {
                                return $set('payment_date', $record->payment_date->format('Y-m-d'));
                            }
                        )
                    )
                    ->formatStateUsing(fn($record) => match ($record?->payment_date ?? null) {
                        null => '',
                        default => $record->payment_date->format('Y-m-d')
                    }),
                Hidden::make('payment_credit_days')
                    ->default(7),
            ]);
    }


    public static function hiddenSection()
    {
        return [
            Forms\Components\Hidden::make('options.updateSellerData')
                ->mutateDehydratedStateUsing(fn($state) => $state ?? false)
                ->default(false),
            Forms\Components\Hidden::make('options.sellerType')
                ->mutateDehydratedStateUsing(fn($state) => $state ?? false)
                ->default(fn($record) => $record->seller_id === null ? 'issuer' : 'partner'),
            Forms\Components\Hidden::make('options.updateBuyerData')
                ->mutateDehydratedStateUsing(fn($state) => $state ?? false)
                ->default(false),
        ];
    }

    public static function sellerSection()
    {
        return
            Fieldset::make(__('app.trade_docs.edit.seller'))
                ->columnSpanFull()
                ->schema([
                    Grid::make(1)
                        ->columnSpanFull()
                        ->schema([
                            Select::make('seller_id')
                                ->hiddenLabel()
                                ->inlineLabel(false)
                                ->options(
                                    PartnersRepository::getPartnerForSelect()
                                )
                                ->columnSpan(1)
                                ->live()
                                ->native(false)
                                ->preload()
                                ->searchable()
                                ->getSearchResultsUsing(
                                    fn($query) => PartnersRepository::searchPartnerForSelect($query)
                                )
                                ->getOptionLabelUsing(
                                    fn($value) => Partner::find($value)?->displayName() ?? ''
                                )
                                ->createOptionForm(PartnerResource::getCreatePartnerModalForm())
                                ->createOptionUsing(function (array $data, $form) {
                                    return PartnersRepository::createFromModalForm($data)?->id;
                                })
                                ->afterStateUpdated(
                                    function (Forms\Set $set, $state, $operation, $record, $get) {
                                        if ($state !== null) {
                                            $set('options.updateSellerData', true);
                                            self::fillSellerData($operation, $record, $state, $set);
                                        } else {
                                            self::resetSellerData($operation, $set);
                                        }
                                        $set('prepaidInvoices', []);
                                    }
                                )
                                ->required(),
                        ]),
                    Grid::make(3)
                        ->schema([
                            TextInput::make('sellerdata.vat_id')
                                ->hiddenLabel()
                                ->label(null)
                                ->readOnly(fn($state) => blank($state))
                                ->inlineLabel(false)
                                ->placeholder('NIP'),
                            TextInput::make('sellerdata.name')
                                ->hiddenLabel()
                                ->label(null)
                                ->readOnly(fn($state) => blank($state))
                                ->inlineLabel(false)
                                ->placeholder('Nazwa'),
                            TextInput::make('sellerdata.address')
                                ->hiddenLabel()
                                ->label(null)
                                ->readOnly(fn($state) => blank($state))
                                ->inlineLabel(false)
                                ->placeholder('Address'),
                            TextInput::make('sellerdata.postcode')
                                ->hiddenLabel()
                                ->label(null)
                                ->readOnly(fn($state) => blank($state))
                                ->inlineLabel(false)
                                ->placeholder('Kod Pocztowy'),
                            TextInput::make('sellerdata.city')
                                ->hiddenLabel()
                                ->label(null)
                                ->readOnly(fn($state) => blank($state))
                                ->inlineLabel(false)
                                ->placeholder('Miasto'),
                        ]),
                ]);
    }


    public static function bankSection(): Fieldset
    {
        return Fieldset::make('Bank')
            ->schema([
                Forms\Components\Grid::make(3)
                    ->schema([
                        Select::make('payment_type')
                            ->label(__('app.trade_docs.create.payment_type'))
                            ->options(PaymentTypes::toArrayWithLabels())
                            ->selectablePlaceholder(false)
                            ->native(false)
                            ->default(2)
                            ->required(),
                        Select::make('currency')
                            ->options(
                                collect(CurrenciesRepository::getSystemCurrencies())
                                    ->pluck('currency', 'alphabeticCode')
                                    ->toArray()
                            )
                            ->required()
                            ->live()
                            ->afterStateUpdated(
                                fn(Set $set, Get $get) => self::findCurrencyExchangeRate($get, $set)
                            )
                            ->label(__('app.trade_docs.create.currency'))
                            ->default('PLN'),
                        TextInput::make('meta.bank_account')
                            ->label(__('app.trade_docs.create.bank_account'))
                            ->formatStateUsing(
                                fn(?PurchaseDoc $record, $operation) => match ($operation) {
                                    'create' => '',
                                    default => $record?->meta?->getBankDataItem('bank_account') ?? '',
                                }
                            )
                            ->hiddenLabel(false),
                        DatePicker::make('currency_rate_date')
                            ->visible(
                                fn(Get $get) => filled($get('currency')) && $get('currency') !== 'PLN'
                            )
                            ->label(__('app.trade_docs._.exchange_rate_date'))
                            ->native(false)
                            ->displayFormat('Y-m-d')
                            ->live()
                            ->afterStateUpdated(
                                fn(Set $set, Get $get) => self::findCurrencyExchangeRate($get, $set)
                            ),
                        TextInput::make('exchange_rate')
                            ->numeric()
                            ->requiredIf(
                                'currency',
                                fn(Get $get) => filled($get('currency')) && $get('currency') !== 'PLN'
                            )
                            ->visible(
                                fn(Get $get) => filled($get('currency')) && $get('currency') !== 'PLN'
                            )
                            ->label(__('app.trade_docs._.exchange_rate')),
                        Forms\Components\Placeholder::make('ex_date_info')
                            ->content(
                                function (Get $get) {
                                    return __('app.trade_docs.create.possible_ex_rate_date') .
                                        ' ' .
                                        $get('currency_rate_date');
                                }
                            )
                            ->visible(
                                fn(Get $get) => filled($get('currency')) && $get('currency') !== 'PLN'
                            )
                            ->hiddenLabel(true),
                    ]),
            ]);


//        return Fieldset::make('Dane bankowe')
//            ->label(__('app.trade_docs.edit.bank_section'))
//            ->schema([
//                Grid::make(1)
//                    ->schema([
//                        Select::make('currency')
//                            ->options(
//                                collect(CurrenciesRepository::getSystemCurrencies())
//                                    ->pluck('currency', 'alphabeticCode')
//                                    ->toArray()
//                            )
//                            ->required()
//                            ->label(__('app.trade_docs.edit.currency'))
//                            ->default('PLN'),
//                        TextInput::make('exchange_rate')
//                            ->readOnly()
//                            ->numeric()
//                            ->label(__('app.trade_docs.edit.exchange_rate'))
//                            ->default(1),
//                        TextInput::make('currency_rate_date')
//                            ->readOnly()
//                            ->label(__('app.trade_docs.edit.currency_rate_date'))
//                            ->default(null),
//                        Grid::make(1)
//                            ->schema([
//                                Forms\Components\TextInput::make('meta.bank_account')
//                                    ->label(__('app.trade_docs.edit.bank_account'))
//                                    ->hiddenLabel(false),
//                            ]),
//                    ]),
//            ]);
    }

    public static function getHeaderPaymentAction(): HeaderAction
    {
        return self::hydratePaymentAction(self::getProperActionObject('header'));
    }

    public static function getTablePaymentAction(): TableAction
    {
        return self::hydratePaymentAction(self::getProperActionObject('table'));
    }

    public static function getFormPaymentAction(): Action
    {
        return self::hydratePaymentAction(self::getProperActionObject('form'));
    }


    public function getInfolistPaymentAction(): InfoListAction
    {
        return self::hydratePaymentAction(self::getProperActionObject('infolist'));
    }

    protected static function hydratePaymentAction(
        HeaderAction|TableAction|InfoListAction|Action $action
    ): HeaderAction|TableAction|InfoListAction|Action {
        return $action
            ->label('Opłać dokument')
            ->fillForm(fn(Model $record): array => [
                'seller' => $record->seller->name,
                'document' => $record->full_doc_number,
                'amount' => $record->gross,
                'payment_date' => Carbon::now('Europe/Warsaw')->format('Y-m-d')
            ])
            ->icon('heroicon-o-currency-dollar')
            ->form([
                Grid::make(2)
                    ->schema([
                        TextInput::make('document')
                            ->readOnly()
                            ->dehydrated(false)
                            ->label('Dokument'),
                        TextInput::make('seller')
                            ->readOnly()
                            ->dehydrated(false)
                            ->label('Sprzedawca'),
                        TextInput::make('amount')
                            ->readOnly()
                            ->label('Kwota'),
                        DatePicker::make('payment_date')
                            ->label('Data płatności')
                            ->native(false)
                            ->displayFormat('Y-m-d')
                            ->required(),
                    ])
            ])
            ->color(Color::Amber)
            ->visible(
                fn(Model $record) => blank($record->payment_date)
            )
            ->action(
                fn(Model $record, $data) => $record->update(
                    ['payment_date' => $data['payment_date'], 'is_paid' => true]
                )
            );
    }


    protected static function getProperActionObject($type): null|HeaderAction|TableAction|InfoListAction|Action
    {
        return match ($type) {
            'header' => HeaderAction::make('do-payment'),
            'table' => TableAction::make('do-payment'),
            'infolist' => InfoListAction::make('do-payment'),
            'form' => Action::make('do-payment'),
            default => null
        };
    }

    protected static function fillSellerData(string $operation, ?Model $record, $state, Forms\Set $set)
    {
        $seller = Partner::find($state);
        $set('sellerdata.name', $seller->name);
        $set('sellerdata.address', $seller->address);
        $set('sellerdata.postcode', $seller->postcode);
        $set('sellerdata.city', $seller->city);
        $set('sellerdata.vat_id', $seller->vat_id);
    }

    protected static function resetSellerData(string $operation, Forms\Set $set)
    {
        $set('sellerdata.name', '');
        $set('sellerdata.address', '');
        $set('sellerdata.postcode', '');
        $set('sellerdata.city', '');
        $set('sellerdata.vat_id', '');
    }


    public static function correctionSection()
    {
        return Forms\Components\Fieldset::make('Korygowany dokument')
            ->columns(3)
            ->visible(fn(Get $get) => (int)$get('type') === DocumentTypes::FZK->value)
            ->extraAttributes(['class' => 'h-full'], true)
            ->schema([
                Forms\Components\TextInput::make('meta.corrected_doc.full_doc_number')
                    ->required(fn(Get $get) => (int)$get('type') === DocumentTypes::FZK->value)
                    ->formatStateUsing(
                        fn(?PurchaseDoc $record) => $record?->getMeta()->corrected_doc['full_doc_number'] ?? ''
                    )
                    ->label('Nr dokumentu'),
                Forms\Components\DatePicker::make('meta.corrected_doc.issued_at')
                    ->label(__('app.trade_docs.create.issued_at'))
                    ->displayFormat('Y-m-d')
                    ->time(false)
                    ->format('Y-m-d')
                    ->native(false)
                    ->closeOnDateSelection()
                    ->default(now('Europe/Warsaw')->format('Y-m-d'))
                    ->formatStateUsing(
                        fn(?PurchaseDoc $record) => $record?->getMeta()->corrected_doc['issued_at'] ?? ''
                    )
                    ->required(),
                Forms\Components\Textarea::make('meta.corrected_doc.notes')
                    ->label('Przyczyna korekty')
                    ->maxLength(255)
                    ->formatStateUsing(fn(?PurchaseDoc $record) => $record?->getMeta()->corrected_doc['notes'] ?? '')
                    ->required(),
            ]);
    }

//    protected static function getBuyerData(string $operation, ?Model $record, Forms\Get $get): string|HtmlString
//    {
//        if ($operation === 'create' || null === $record) {
//            return new HtmlString(tenant(true)->getInvoiceText('html'));
//        }
//
//        return new HtmlString($record->meta->getBuyerData('html'));
//    }

//    public static function buyerSection()
//    {
//        return
//            Fieldset::make(fn() => __('app.trade_docs.edit.buyer'))
//                ->schema([
//                    Grid::make(3)
//                        ->schema([
//                            Grid::make(2)
//                                ->columnSpan(2)
//                                ->schema([
//                                    Forms\Components\Placeholder::make('buyerData')
//                                        ->content(
//                                            function (?Model $record, Forms\Get $get, $operation) {
//                                                return self::getBuyerData($operation, $record, $get);
//                                            }
//                                        )
//                                        ->hiddenLabel(true),
//                                ])
//                        ])
//                ]);
//    }

//    public static function ksefSection()
//    {
//        return Fieldset::make('KSEF')
//            ->columnSpan(1)
//            ->schema([
//                Grid::make(1)
//                    ->schema([
//                        TextInput::make('ksef_ref')
//                            ->maxLength(255)
//                            ->default(null),
//                        TextInput::make('ksef_status')
//                            ->required()
//                            ->numeric()
//                            ->default(0),
//                        TextInput::make('ksef_inv_number')
//                            ->maxLength(255)
//                            ->default(null),
//                    ]),
//            ]);
//    }
}
