<?php

namespace App\Filament\App\Resources\TradeDocResource\Pages;

use App\Enums\DocumentTypes;
use App\Enums\SystemModules;
use App\Enums\TradeDocVatMethod;
use App\Filament\App\Resources\TradeDocResource;
use App\Filament\Traits\HasBackToListButton;
use App\Models\TradeDoc;
use App\Models\TradeDocItem;
use App\Repositories\TradeDocsRepository;
use Filament\Forms\Form;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class CreateTradeDocWithItems extends CreateRecord
{
    use HasBackToListButton;

    protected static string $resource = TradeDocResource::class;

    public $ex_rate_date = null;

    public static function getNavigationLabel(): string
    {
        return __('app.trade_docs.navigation.create-sell-label');
    }

    public static function getNavigationIcon(): string|Htmlable|null
    {
        return 'heroicon-o-document-text';
    }

    public static function shouldRegisterNavigation(array $parameters = []): bool
    {
        return tenant()?->hasModule(SystemModules::INVOICES) ?? false;
    }

    /**
     * @return string|\Illuminate\Contracts\Support\Htmlable
     */
    public function getHeading(): string|\Illuminate\Contracts\Support\Htmlable
    {
        return __('app.trade_docs.create.page.heading');
    }

    public function form(Form $form): Form
    {
        return TradeDocResource\Forms\TradeDocForm::createFormWithItems($form);
    }

    public function handleRecordCreation(array $data): TradeDoc
    {
        return DB::transaction(function () use ($data) {
            // Extract items data before creating the main record
            $items = $data['items'] ?? [];
            unset($data['items']);

            // Extract meta data
            $meta = $data['meta'] ?? [];
            unset($data['meta']);

            // Handle prepaid invoices if applicable
            $prepaidInvoices = [];
            if ($data['is_final_invoice'] ?? false) {
                $prepaidInvoices = $data['prepaidInvoices'] ?? [];
            }
            unset($data['prepaidInvoices']);

            // Create the main trade document record
            $record = new TradeDoc(Arr::except($data, 'buyerdata'));
            $record->installation = auth()->user()?->installation();
            $record->creator_id = auth()->user()->id;
            $record->issuer_id = tenant()->id;

            if ($record->type === DocumentTypes::FAUP) {
                $record->vat_method = TradeDocVatMethod::BASE_ON_GROSS;
            }

            TradeDocsRepository::createDocNumberOnModel($record);
            TradeDocsRepository::createTransactionOnModel($record);
            $record->save();

            // Create meta data
            TradeDocsRepository::createMetaOnModel($record, $meta, $data);

            // Handle prepaid invoices if applicable
            if (filled($prepaidInvoices)) {
                TradeDocsRepository::finalInvoiceProcess($record, $prepaidInvoices);
            }

            // Create line items
            foreach ($items as $itemData) {
                if (filled($itemData['label']) && filled($itemData['amount'])) {
                    TradeDocsRepository::createDocItem($record, $itemData);
                }
            }

            return $record;
        });
    }

    protected function getRedirectUrl(): string
    {
        // Redirect to the add-item page to allow further editing if needed
        return self::getResource()::getUrl('add-item', ['record' => $this->getRecord()->transaction_id]);
    }

    protected function getCreateFormAction(): \Filament\Actions\Action
    {
        return parent::getCreateFormAction()
            ->label('Utwórz fakturę');
    }

    protected function getCreateAnotherFormAction(): \Filament\Actions\Action
    {
        return parent::getCreateAnotherFormAction()
            ->label('Utwórz i dodaj kolejną');
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Ensure items array exists even if empty
        if (!isset($data['items'])) {
            $data['items'] = [];
        }

        return $data;
    }
}
