<?php

namespace App\Filament\App\Resources\TradeDocResource\Forms;

use App\Enums\DocumentTypes;
use App\Enums\PaymentTypes;
use App\Enums\SystemModules;
use App\Enums\TradeDocVatMethod;
use App\Filament\App\Resources\PartnerResource;
use App\Models\DocumentSeriesPattern;
use App\Models\Partner;
use App\Models\TradeDoc;
use App\Repositories\CurrenciesRepository;
use App\Repositories\CurrencyRatesExchangeRepository;
use App\Repositories\DocumentSeriesRepository;
use App\Repositories\PartnersRepository;
use App\Services\Filters;
use Filament\Actions\Action as HeaderAction;
use Filament\Forms;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Infolists\Components\Actions\Action as InfoListAction;
use Filament\Support\Colors\Color;
use Filament\Tables\Actions\Action as TableAction;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Illuminate\Support\HtmlString;

class TradeDocForm
{

    protected static array $docTypes = [];

    public static function editForm(Form $form): Form
    {
        return $form
            ->schema([
                ...self::baseSchema($form)
            ])
            ->inlineLabel(true)
            ->columns([
                'sm' => 1,
                'md' => 2,
                'lg' => 3,
            ]);
    }


    public static function baseSchema(Form $form): array
    {
        return [
            Forms\Components\Grid::make(3)
                ->schema([
                    self::buyerSection()
                        ->extraAttributes(['class' => 'h-full'], true)
                        ->columnSpan(2),
                    self::datesSection(),
                ]),
            Forms\Components\Grid::make(3)
                ->visible(function (?TradeDoc $record, $operation) {
                    return $operation === 'edit' && $record?->type === DocumentTypes::FVK;
                })
                ->schema([
                    self::correctionSection(),
                ]),
            self::bankSection(),
            self::optionsSection(),
        ];
    }


    public static function createForm(Form $form): Form
    {
        return $form
            ->schema([
                self::docTypeSection(),
                ...self::baseSchema($form)
            ])
            ->inlineLabel(true)
            ->columns([
                'sm' => 1,
                'md' => 2,
                'lg' => 3,
            ]);
    }

    public static function getDefaultForDocType()
    {
        if (blank(self::$docTypes)) {
            self::getDocTypes();
        }

        if (count(self::$docTypes) === 1) {
            return array_key_first(self::$docTypes);
        }

        return null;
    }

    public static function getDocTypes()
    {
        if (blank(self::$docTypes)) {
            self::$docTypes = DocumentSeriesRepository::getTDocForCreateSelect(tenant(), [DocumentTypes::FVK->value]);
        }
        return self::$docTypes;
    }


    public static function docTypeSection(): Grid
    {
        return Forms\Components\Grid::make([
            'sm' => 1,
            'md' => 2,
            'lg' => 3,
        ])
            ->columnSpanFull()
            ->schema([
                Fieldset::make('Typ dokumentu')
                    ->columns(3)
                    ->schema([
                        Select::make('type')
                            ->default(
                                fn(Set $set) => self::getDefaultForDocType()
                            )
                            ->columnSpan(1)
                            ->label(__('app.trade_docs.create.type'))
                            ->options(
                                self::getDocTypes()
                            )
                            ->afterStateUpdated(function (Set $set, $state) {
                                if (blank($state)) {
                                    $set('document_series_id', '');
                                    return;
                                }

                                $set(
                                    'document_series_id',
                                    DocumentSeriesRepository::getDefaultSeriesForDocType($state)?->id ?? ''
                                );
                            })
                            ->live()
                            ->required(),
                        Select::make('document_series_id')
                            ->label(__('app.trade_docs.create.document_series'))
                            ->native(false)
                            ->options(function (Get $get) {
                                return match (blank($get('type'))) {
                                    true => [],
                                    default => DocumentSeriesRepository::getSeriesForDocType($get('type'), true)
                                        ->pluck('name', 'id')
                                };
                            })
                            ->default(
                                fn(Get $get) => match (blank($get('type'))) {
                                    true => null,
                                    default => DocumentSeriesRepository::getDefaultSeriesForDocType($get('type'))?->id
                                }
                            )
                            ->live()
                            ->required(),

                        Forms\Components\Placeholder::make('pattern')
                            ->label(__('app.trade_docs.create.pattern'))
                            ->content(
                                function (Get $get): string {
                                    $patternId = $get('document_series_id');
                                    $pattern = match (blank($patternId)) {
                                        true => '--------',
                                        default => DocumentSeriesPattern::find(
                                            $patternId
                                        )->pattern
                                    };
                                    return $pattern;
                                }
                            ),
                    ])
            ]);
    }


    public static function getDefaultPDD(Get $get): string
    {
        return Carbon::make($get('issued_at'))
            ?->addDays(7)
            ->format('Y-m-d');
    }


    public static function findCurrencyExchangeRate(Get $get, Set $set): void
    {
        if ($get('currency') === 'PLN') {
            $set('exchange_rate', 1);
            return;
        }
        $target = $get('currency');
        $src = 'PLN';
        $date = $get('currency_rate_date');
        if (blank($date)) {
            $date = (new Carbon($get('issued_at')))->subDay()->format('Y-m-d');
            $set('currency_rate_date', $date);
        }
        [$rate, $edate] = CurrencyRatesExchangeRepository::getNearestRate($src, $target, $date);

        $set(
            'exchange_rate',
            $rate ?? 1
        );
        $set('currency_rate_date', $edate);
    }


    public static function setPaymentDueDate(Get $get, $state): string
    {
        return Carbon::make($get('issued_at'))
            ?->endOfDay()
            ->addDays($state)->format('Y-m-d');
    }


    public static function setPaymentCreditDays(Get $get, $state): string
    {
        return Carbon::make($state)
            ?->diffInDays(Carbon::make($get('issued_at'))->startOfDay());
    }


    public static function optionsSection(): Forms\Components\Section
    {
        return Forms\Components\Section::make('Opcje')
            ->collapsed(false)
            ->collapsible(true)
            ->columns(2)
            ->schema([
                Forms\Components\Grid::make(1)
                    ->columnSpan(1)
                    ->columns(1)
                    ->schema([
                        Forms\Components\Checkbox::make('meta.options.reverse_charge')
                            ->default(false)
                            ->hidden(
                                function (string $operation, ?TradeDoc $record, Get $get) {
                                    return match ($operation) {
                                        'create' => blank($get('type')) ||
                                            (int)$get('type') === DocumentTypes::FAUP->value ||
                                            (int)$get('type') === DocumentTypes::PROF->value,
                                        default => $record?->type === DocumentTypes::FAUP ||
                                            (int)$get('type') === DocumentTypes::PROF->value,
                                    };
                                }
                            )
                            ->formatStateUsing(
                                fn(?TradeDoc $record) => $record?->getMeta()->getOption('reverse_charge') ?? false
                            )
                            ->columnSpan(1)
                            ->inlineLabel(false)
                            ->label(__('app.trade_docs._.reverse_charge')),
                        Forms\Components\Checkbox::make('meta.options.mpp')
                            ->default(false)
                            ->formatStateUsing(
                                fn(?TradeDoc $record) => $record?->getMeta()->getOption('mpp') ?? false
                            )
                            ->hidden(
                                function (string $operation, ?TradeDoc $record, Get $get) {
                                    return match ($operation) {
                                        'create' => blank($get('type')) ||
                                            (int)$get('type') === DocumentTypes::FAUP->value ||
                                            (int)$get('type') === DocumentTypes::PROF->value,
                                        default => $record?->type === DocumentTypes::FAUP ||
                                            (int)$get('type') === DocumentTypes::PROF->value,
                                    };
                                }
                            )
                            ->columnSpan(1)
                            ->inlineLabel(false)
                            ->label(__('app.trade_docs._.mpp')),
                        Forms\Components\Checkbox::make('is_final_invoice')
                            ->default(false)
                            ->live()
                            ->visible(
                                function (string $operation, ?TradeDoc $record, Get $get) {
                                    return match ($operation) {
                                        'create' => (int)$get('type') === DocumentTypes::FVS->value,
                                        default => $record?->type === DocumentTypes::FVS,
                                    };
                                }
                            )
                            ->columnSpan(1)
                            ->inlineLabel(false)
                            ->helperText(fn(Get $get) => match (blank($get('buyer_id'))) {
                                true => 'Wybierz najpierw nabywcę',
                                default => null,
                            })
                            ->label(__('app.trade_docs._.final_invoice')),
                        Select::make('prepaidInvoices')
                            ->options(
                                fn(Get $get, $operation, ?TradeDoc $record) => match (blank($get('buyer_id'))) {
                                    true => [],
                                    default => TradeDoc::where('type', DocumentTypes::FVP)
                                        ->where('buyer_id', $get('buyer_id'))
                                        ->when(
                                            $operation === 'create',
                                            fn($query) => $query->whereNull('final_invoice_trade_doc_id'),
                                            fn($query) => $query->where(function ($query) use ($record) {
                                                $query->where('final_invoice_trade_doc_id', null)
                                                    ->orWhereIn('uuid', $record->prepaidInvoices->pluck('uuid'));
                                            })
                                        )
                                        ->pluck('full_doc_number', 'uuid')
                                        ->toArray()
                                }
                            )
                            ->multiple()
                            ->preload()
                            ->requiredIf(
                                'is_final_invoice',
                                true
                            )
                            ->helperText(
                                function ($operation) {
                                    if ($operation === 'edit') {
                                        return 'Po zmianie listy faktur zaliczkowych ' .
                                            'pamiętaj o zmianie pozycji na fakturze!!!';
                                    }
                                    return '';
                                }
                            )
                            ->formatStateUsing(
                                fn(?TradeDoc $record) => $record?->prepaidInvoices->pluck('uuid')->toArray() ?? []
                            )
                            ->label(__('app.trade_docs._.prepaid_invoices'))
                            ->visible(
                                function (Get $get) {
                                    return $get('is_final_invoice') && filled($get('buyer_id'));
                                }
                            ),
                        Forms\Components\Checkbox::make('final_invoice_trade_doc_id')
                            ->visible(
                                function (Get $get, $operation) {
                                    return $operation === 'edit' && (int)$get('type') === DocumentTypes::FVP->value;
                                }
                            )
                            ->disabled(true)
                            ->columnSpan(1)
                            ->label(
                                function (TradeDoc $record) {
                                    return __('app.trade_docs._.final_invoice') . match ($record->finalInvoice) {
                                            null => ' BRAK',
                                            default => ' ' . $record->finalInvoice->full_doc_number
                                    };
                                }
                            ),
                        Forms\Components\Checkbox::make('payment_reminder_enabled')
                            ->label('Włącz przypomnienie o płatności')
                            ->live()
                            ->visible(
                                function (Get $get, ?TradeDoc $record) {
                                    if ((tenant()?->hasModule(SystemModules::PAYMENT_REMINDER) ?? false) === false) {
                                        return false;
                                    }
                                    if (blank($get('buyer_id'))) {
                                        return false;
                                    }
                                    if (($record?->is_paid ?? false) && $record?->payment_reminder_enabled === null) {
                                        return false;
                                    }
                                    $buyer = \App\Models\Partner::find($get('buyer_id'));
                                    return $buyer && filled($buyer->email);
                                }
                            )
                            ->disabled(fn(?TradeDoc $record) => $record?->is_paid ?? false)
                            ->helperText(
                                function (Get $get) {
                                    if (blank($get('buyer_id'))) {
                                        return 'Wybierz najpierw nabywcę';
                                    }
                                    $buyer = \App\Models\Partner::find($get('buyer_id'));
                                    if (!$buyer || blank($buyer->email)) {
                                        return 'Nabywca nie ma podanego adresu email';
                                    }
                                    return "Email zostanie wysłany na: {$buyer->email}";
                                }
                            )
                            ->columnSpan(1)
                            ->default(false),
                        Select::make('payment_reminder_days')
                            ->label('Wyślij przypomnienie')
                            ->options([
                                1 => '1 dzień przed terminem',
                                3 => '3 dni przed terminem',
                            ])
                            ->default(3)
                            ->visible(function (Get $get) {
                                return (tenant()?->hasModule(SystemModules::PAYMENT_REMINDER) ?? false) &&
                                    $get('payment_reminder_enabled');
                            })
                            ->disabled(fn(?TradeDoc $record) => $record?->is_paid ?? false)
                            ->required(fn(Get $get) => $get('payment_reminder_enabled'))
                            ->columnSpan(1),
                    ]),
                Forms\Components\Grid::make(1)
                    ->columnSpan(1)
                    ->schema([
                        Select::make('vat_method')
                            ->label(__('app.trade_docs.create.vat_method'))
                            ->options(TradeDocVatMethod::toArrayWithLabels())
                            ->hidden(
                                fn(Get $get) => blank($get('type')) ||
                                    (int)$get('type') === DocumentTypes::FAUP->value
                            )
                            ->required()
                            ->default(1)
                            ->live(),
                        Forms\Components\Textarea::make('meta.note')
                            ->label(__('app.trade_docs._.notes'))
                            ->maxLength(255)
                            ->formatStateUsing(
                                fn(?TradeDoc $record) => $record?->getMeta()->getNote() ?? ''
                            )
                            ->columnSpanFull()
                            ->default(null),
                    ]),
            ]);
    }


    public static function datesSection(): Fieldset
    {
        return Fieldset::make('Daty')
            ->columns(1)
            ->columnSpan(1)
            ->extraAttributes(['class' => 'h-full'], true)
            ->schema([
                DatePicker::make('issued_at')
                    ->label(__('app.trade_docs.create.issued_at'))
                    ->displayFormat('Y-m-d')
                    ->time(false)
                    ->format('Y-m-d')
                    ->native(false)
                    ->closeOnDateSelection()
                    ->live()
                    ->afterStateUpdated(
                        function (Set $set, Get $get, $state) {
                            $set(
                                'payment_due_date',
                                self::setPaymentDueDate($get, $get('payment_credit_days'))
                            );
                            $date = (new Carbon($state))->subDay()->format('Y-m-d');
                            $set('currency_rate_date', $date);
                            self::findCurrencyExchangeRate($get, $set);
                        }
                    )
                    ->default(now('Europe/Warsaw')->format('Y-m-d'))
                    ->required(),
                DatePicker::make('sells_date')
                    ->label(__('app.trade_docs.create.sells_date'))
                    ->displayFormat('Y-m-d')
                    ->native(false)
                    ->closeOnDateSelection()
                    ->default(now('Europe/Warsaw')->format('Y-m-d'))
                    ->required(),
                DatePicker::make('payment_due_date')
                    ->label(__('app.trade_docs.create.payment_due_date'))
                    ->native(false)
                    ->displayFormat('Y-m-d')
                    ->live()
                    ->closeOnDateSelection()
                    ->default(
                        fn(Get $get) => self::getDefaultPDD($get)
                    )
                    ->afterStateUpdated(
                        function (Set $set, Get $get, $state) {
                            $set('payment_credit_days', self::setPaymentCreditDays($get, $state));
                        }
                    )
                    ->required(),
                TextInput::make('payment_date')
                    ->label(__('app.trade_docs.create.is_paid'))
                    ->visible(fn($operation) => $operation === 'edit')
                    ->readOnly(true)
                    ->suffixAction(
                        TradeDocForm::getFormPaymentAction()->after(
                            function (Set $set, $record) {
                                return $set('payment_date', $record->payment_date->format('Y-m-d'));
                            }
                        )
                    )
                    ->formatStateUsing(fn ($record) => match ($record?->payment_date ?? null) {
                        null => '',
                        default => $record->payment_date->format('Y-m-d')
                    }),
                Hidden::make('payment_credit_days')
                    ->default(7),
            ]);
    }


    public static function correctionSection()
    {
        return Fieldset::make('Dokument korygowany')
            ->visible(function (TradeDoc $record) {
                return $record->type === DocumentTypes::FVK;
            })
            ->columns(3)
            ->extraAttributes(['class' => 'h-full'], true)
            ->schema([
                TextInput::make('source_id')
                    ->label('Numer dokumentu')
                    ->dehydrated(false)
                    ->visible(function (?TradeDoc $record) {
                        return $record?->type === DocumentTypes::FVK;
                    })
                    ->readOnly()
                    ->formatStateUsing(
                        fn(?TradeDoc $record) => $record?->getSourceDoc()?->full_doc_number ?? ''
                    ),
                TextInput::make('source_issued_at')
                    ->label('Data dokumentu')
                    ->dehydrated(false)
                    ->visible(function (TradeDoc $record) {
                        return $record->type === DocumentTypes::FVK;
                    })
                    ->readOnly()
                    ->formatStateUsing(
                        function (?TradeDoc $record) {
                            return $record?->getSourceDoc()?->issued_at?->format('Y-m-d') ?? '';
                        }
                    ),
                TextInput::make('notes')
                    ->label('Przyczyna korekty')
                    ->visible(function (TradeDoc $record) {
                        return $record->type === DocumentTypes::FVK;
                    })
                    ->hiddenLabel(false),
            ]);
    }


    public static function buyerSection()
    {
        return
            Fieldset::make(__('app.trade_docs.edit.buyer'))
                ->columnSpanFull()
                ->schema([
                    Grid::make(1)
                        ->columnSpanFull()
                        ->schema([
                            Select::make('buyer_id')
                                ->hiddenLabel()
                                ->inlineLabel(false)
                                ->options(
                                    PartnersRepository::getPartnerForSelect()
                                )
                                ->columnSpan(1)
                                ->live()
                                ->native(false)
                                ->preload()
                                ->searchable()
                                ->getSearchResultsUsing(
                                    fn($query) => PartnersRepository::searchPartnerForSelect($query)
                                )
                                ->getOptionLabelUsing(
                                    fn($value) => Partner::find($value)?->displayName() ?? ''
                                )
                                ->createOptionForm(PartnerResource::getCreatePartnerModalForm())
                                ->createOptionUsing(function (array $data, $form) {
                                    return PartnersRepository::createFromModalForm($data)?->id;
                                })
                                ->afterStateUpdated(
                                    function (Forms\Set $set, $state, $operation, $record, $get) {
                                        if ($state !== null) {
                                            $set('options.updateBuyerData', true);
                                            self::fillBuyerData($operation, $record, $state, $set);
                                        } else {
                                            self::resetBuyerData($operation, $set);
                                        }
                                        $set('prepaidInvoices', []);
                                    }
                                )
                                ->required(),
                        ]),
                    Grid::make(3)
                        ->schema([
                            Grid::make(1)
                                ->columnSpan(1)
                                ->schema([
                                    Forms\Components\Textarea::make('buyerdata.name')
                                        ->hiddenLabel()
                                        ->label(null)
                                        ->rows(3)
                                        ->readOnly(fn($state) => blank($state))
                                        ->inlineLabel(false)
                                        ->placeholder('Nazwa'),
                                ]),
                            Grid::make(2)
                                ->columnSpan(2)
                                ->schema([
                                    TextInput::make('buyerdata.vat_id')
                                        ->hiddenLabel()
                                        ->label(null)
                                        ->readOnly(fn($state) => blank($state))
                                        ->inlineLabel(false)
                                        ->placeholder('NIP'),
                                    TextInput::make('buyerdata.address')
                                        ->hiddenLabel()
                                        ->label(null)
                                        ->readOnly(fn($state) => blank($state))
                                        ->inlineLabel(false)
                                        ->placeholder('Address'),
                                    TextInput::make('buyerdata.postcode')
                                        ->hiddenLabel()
                                        ->label(null)
                                        ->readOnly(fn($state) => blank($state))
                                        ->inlineLabel(false)
                                        ->placeholder('Kod Pocztowy'),
                                    TextInput::make('buyerdata.city')
                                        ->hiddenLabel()
                                        ->label(null)
                                        ->readOnly(fn($state) => blank($state))
                                        ->inlineLabel(false)
                                        ->placeholder('Miasto'),
                                ])
                        ]),
                ]);
    }


    /**
     * @TODO: not used now. DO NOT REMOVE
     * @return Fieldset
     */
    public static function sellerSection()
    {
        return
            Fieldset::make(fn() => __('app.trade_docs.edit.seller'))
                ->schema([
                    Grid::make(3)
                        ->schema([
                            Grid::make(2)
                                ->columnSpan(2)
                                ->schema([
                                    Checkbox::make('meta.options.different_seller')
                                        ->default(false)
                                        ->disabled(true)
                                        ->live()
                                        ->columnSpanFull()
                                        ->afterStateUpdated(
                                            function (Forms\Set $set, $state) {
                                                $set('options.updateSellerData', $state);
                                                if (false === $state) {
                                                    $set('seller_id', null);
                                                    $set('options.sellerType', 'issuer');
                                                    $set('options.updateSellerData', true);
                                                } else {
                                                    $set('options.sellerType', 'partner');
                                                }
                                            }
                                        )
                                        ->formatStateUsing(
                                            function ($record, $operation) {
                                                if ($operation !== 'create') {
                                                    return $record->getMeta()->options['different_seller'] ?? false;
                                                }
                                            }
                                        )
                                        ->label(__('app.trade_docs.edit.different_seller')),
                                    Select::make('seller_id')
                                        ->relationship('seller', 'name')
                                        ->options(
                                            Partner::all()->pluck('name', 'id')
                                        )
                                        ->hiddenLabel()
                                        ->live()
                                        ->preload()
                                        ->createOptionForm(PartnerResource::getCreatePartnerModalForm())
                                        ->createOptionUsing(function (array $data) {
                                            return PartnersRepository::createFromModalForm($data)?->id;
                                        })
                                        ->afterStateUpdated(
                                            function (Forms\Set $set, $state) {
                                                if ($state !== null) {
                                                    $set('options.updateSellerData', true);
                                                }
                                            }
                                        )
                                        ->columnSpanFull()
                                        ->visible(
                                            fn(Forms\Get $get) => $get('meta.options.different_seller') === true
                                        )
                                        ->required(
                                            fn(Forms\Get $get) => $get('meta.options.different_seller') === true
                                        ),
                                ]),
                            Forms\Components\Placeholder::make('sellerData')
                                ->content(
                                    function (?Model $record, Forms\Get $get, $operation) {
                                        return self::getSellerData($operation, $record, $get);
                                    }
                                )
                                ->hiddenLabel(true),
                        ])
                ]);
    }


    public static function bankSection(): Fieldset
    {
        return Fieldset::make('Bank')
            ->schema([
                Forms\Components\Grid::make(3)
                    ->schema([
                        Select::make('payment_type')
                            ->label(__('app.trade_docs.create.payment_type'))
                            ->options(PaymentTypes::toArrayWithLabels())
                            ->selectablePlaceholder(false)
                            ->native(false)
                            ->default(2)
                            ->required(),
                        Select::make('currency')
                            ->options(
                                collect(CurrenciesRepository::getSystemCurrencies())
                                    ->pluck('currency', 'alphabeticCode')
                                    ->toArray()
                            )
                            ->required()
                            ->live()
                            ->afterStateUpdated(
                                fn(Set $set, Get $get) => self::findCurrencyExchangeRate($get, $set)
                            )
                            ->label(__('app.trade_docs.create.currency'))
                            ->default('PLN'),
                        Select::make('meta.bank_account')
                            ->placeholder('Wybierz konto bankowe')
                            ->options(
                                tenant()?->getBankAccounts()
                                    ->pluck('account_name', 'bank_account')
                                    ->toArray() ?? []
                            )
                            ->helperText(fn(Get $get) => $get('meta.bank_account') ?? '')
                            ->label(new HtmlString('&nbsp;'))
//                                    ->label(__('app.trade_docs.create.bank_account'))
                            ->live()
                            ->formatStateUsing(
                                fn(?TradeDoc $record, $operation) => match ($operation) {
                                    'create' => tenant()->getBankAccounts()->pluck('bank_account')->first(),
                                    default => $record?->meta?->getBankDataItem('bank_account') ?? '',
                                }
                            )
                            ->hiddenLabel(false),
                        DatePicker::make('currency_rate_date')
                            ->visible(
                                fn(Get $get) => filled($get('currency')) && $get('currency') !== 'PLN'
                            )
                            ->label(__('app.trade_docs._.exchange_rate_date'))
                            ->native(false)
                            ->displayFormat('Y-m-d')
                            ->live()
                            ->afterStateUpdated(
                                fn(Set $set, Get $get) => self::findCurrencyExchangeRate($get, $set)
                            ),
                        TextInput::make('exchange_rate')
                            ->numeric()
                            ->requiredIf(
                                'currency',
                                fn(Get $get) => filled($get('currency')) && $get('currency') !== 'PLN'
                            )
                            ->visible(
                                fn(Get $get) => filled($get('currency')) && $get('currency') !== 'PLN'
                            )
                            ->label(__('app.trade_docs._.exchange_rate')),
                        Forms\Components\Placeholder::make('ex_date_info')
                            ->content(
                                function (Get $get) {
                                    return __('app.trade_docs.create.possible_ex_rate_date') .
                                        ' ' .
                                        $get('currency_rate_date');
                                }
                            )
                            ->visible(
                                fn(Get $get) => filled($get('currency')) && $get('currency') !== 'PLN'
                            )
                            ->hiddenLabel(true),
                    ]),
            ]);
    }


    public static function getHeaderPaymentAction(): HeaderAction
    {
        return self::hydratePaymentAction(self::getProperActionObject('header'));
    }


    public static function getTablePaymentAction(): TableAction
    {
        return self::hydratePaymentAction(self::getProperActionObject('table'));
    }


    public static function getFormPaymentAction(): Action
    {
        return self::hydratePaymentAction(self::getProperActionObject('form'));
    }


    public function getInfolistPaymentAction(): InfoListAction
    {
        return self::hydratePaymentAction(self::getProperActionObject('infolist'));
    }


    protected static function hydratePaymentAction(
        HeaderAction|TableAction|InfoListAction|Action $action
    ): HeaderAction|TableAction|InfoListAction|Action {
        return $action
            ->label('Opłać')
            ->fillForm(fn(Model $record): array => [
                'buyer' => $record->buyer->name,
                'document' => $record->full_doc_number,
                'amount' => $record->gross,
                'payment_date' => Carbon::now('Europe/Warsaw')->format('Y-m-d')
            ])
            ->icon('heroicon-o-currency-dollar')
            ->form([
                Grid::make(2)
                    ->schema([
                        TextInput::make('document')
                            ->readOnly()
                            ->dehydrated(false)
                            ->label('Dokument'),
                        TextInput::make('buyer')
                            ->readOnly()
                            ->dehydrated(false)
                            ->label('Klient'),
                        TextInput::make('amount')
                            ->readOnly()
                            ->label('Kwota'),
                        DatePicker::make('payment_date')
                            ->label('Data płatności')
                            ->native(false)
                            ->displayFormat('Y-m-d')
                            ->required(),
                    ])
            ])
            ->color(Color::Amber)
            ->visible(
                fn(Model $record) => blank($record->payment_date)
            )
            ->action(
                fn(Model $record, $data) => $record->markPaid(Carbon::parse($data['payment_date']))
            );
    }


    protected static function getProperActionObject($type): null|HeaderAction|TableAction|InfoListAction|Action
    {
        return match ($type) {
            'header' => HeaderAction::make('do-payment'),
            'table' => TableAction::make('do-payment'),
            'infolist' => InfoListAction::make('do-payment'),
            'form' => Action::make('do-payment'),
            default => null
        };
    }


    protected static function getBuyerData(string $operation, ?Model $record, Forms\Get $get): string|HtmlString
    {
        if ($operation === 'create') {
            return $get('options.updateBuyerData')
                ? new HtmlString(Partner::find($get('buyer_id'))?->getInvoiceText('html') ?? 'brak')
                : '';
        }
        return $get('options.updateBuyerData')
            ? new HtmlString(Partner::find($get('buyer_id'))->getInvoiceText('html'))
            : new HtmlString($record?->meta->getBuyerData('html'));
    }


    protected static function fillBuyerData(string $operation, ?Model $record, $state, Forms\Set $set)
    {
        $buyer = Partner::find($state);
        $set('buyerdata.name', $buyer->name);
        $set('buyerdata.address', $buyer->address);
        $set('buyerdata.postcode', $buyer->postcode);
        $set('buyerdata.city', $buyer->city);
        $set('buyerdata.vat_id', $buyer->vat_id);
    }


    protected static function resetBuyerData(string $operation, Forms\Set $set)
    {
        $set('buyerdata.name', '');
        $set('buyerdata.address', '');
        $set('buyerdata.postcode', '');
        $set('buyerdata.city', '');
        $set('buyerdata.vat_id', '');
    }


    protected static function getSellerData(string $operation, ?Model $record, Forms\Get $get): string|HtmlString
    {
        if ($operation === 'create' || null === $record) {
            if ($get('options.sellerType') === 'issuer') {
                return new HtmlString(tenant(true)->getInvoiceText('html'));
            }
            return $get('options.updateSellerData')
                ? new HtmlString(Partner::find($get('seller_id'))?->getAddressText('html') ?? 'brak')
                : new HtmlString(tenant(true)->getInvoiceText('html'));
        }

        if (($get('options.updateSellerData') ?? false) === false) {
            return new HtmlString($record->meta->getSellerData('html'));
        }

        if ($get('options.updateSellerData') === true &&
            $get('options.sellerType') === 'issuer') {
            return new HtmlString(tenant(true)->getInvoiceText('html'));
        }

        if ($get('options.updateSellerData') === true &&
            $get('options.sellerType') === 'partner') {
            if ($get('seller_id') === null) {
                return new HtmlString('');
            }
            $seller = Partner::find($get('seller_id'));
            return new HtmlString($seller->getInvoiceText('html'));
        }

        return '';
    }


    //    public static function extraSection()
//    {
//        return Fieldset::make('Dodatkowe')
//            ->columnSpan(1)
//            ->schema([
//                Grid::make(1)
//                    ->schema([
//                        Forms\Components\Textarea::make('meta.note')
//                            ->formatStateUsing(
//                                fn($record) => $record->getMeta()->note
//                            )
//                            ->maxLength(255)
//                            ->default(null),
//                    ]),
//            ]);
//    }


//    public static function ksefSection()
//    {
//        return Fieldset::make('KSEF')
//            ->columnSpan(1)
//            ->schema([
//                Grid::make(1)
//                    ->schema([
//                        TextInput::make('ksef_ref')
//                            ->maxLength(255)
//                            ->default(null),
//                        TextInput::make('ksef_status')
//                            ->required()
//                            ->numeric()
//                            ->default(0),
//                        TextInput::make('ksef_inv_number')
//                            ->maxLength(255)
//                            ->default(null),
//                    ]),
//            ]);
//    }

//    public static function vatSection()
//    {
//        return Fieldset::make('Dane VAT')
//            ->columnSpan(1)
//            ->schema([
//                Grid::make(1)
//                    ->schema([
//                        Select::make('vat_method')
//                            ->options(TradeDocVatMethod::toArrayWithLabels())
//                            ->label(__('app.trade_docs.edit.vat_method'))
//                            ->required()
//                            ->default(1),
//                        TextInput::make('net')
//                            ->label(__('app.trade_docs.edit.net'))
//                            ->numeric()
//                            ->readOnly()
//                            ->default(null),
//                        TextInput::make('vat_amount')
//                            ->label(__('app.trade_docs.edit.vat_amount'))
//                            ->numeric()
//                            ->readOnly()
//                            ->default(null),
//                        TextInput::make('gross')
//                            ->label(__('app.trade_docs.edit.gross'))
//                            ->required()
//                            ->readOnly()
//                            ->numeric(),
//                    ])
//            ]);
//    }

//    public static function hiddenSection()
//    {
//        return [
//            Hidden::make('options.updateSellerData')
//                ->mutateDehydratedStateUsing(fn($state) => $state ?? false)
//                ->default(false),
//            Hidden::make('options.sellerType')
//                ->mutateDehydratedStateUsing(fn($state) => $state ?? false)
//                ->default(fn($record) => $record->seller_id === null ? 'issuer' : 'partner'),
//            Hidden::make('options.updateBuyerData')
//                ->mutateDehydratedStateUsing(fn($state) => $state ?? false)
//                ->default(false),
//        ];
//    }

//    public static function paymentSection()
//    {
//        return Fieldset::make(__('app.trade_docs.edit.payment_section'))
//            ->schema([
//                Grid::make(1)
//                    ->schema([
//                        Select::make('payment_type')
//                            ->label(__('app.trade_docs.edit.payment_type'))
//                            ->options(PaymentTypes::toArrayWithLabels())
//                            ->default(2)
//                            ->required(),
//                        TextInput::make('payment_credit_days')
//                            ->label(__('app.trade_docs.edit.payment_credit_days'))
//                            ->required()
//                            ->default(7)
//                            ->minValue(0)
//                            ->live()
//                            ->afterStateUpdated(
//                                function (Forms\Set $set, Forms\Get $get, $state) {
//                                    $set(
//                                        'payment_due_date',
//                                        Carbon::make($get('issued_at'))
//                                            ->addDays($state)
//                                            ->format('Y-m-d')
//                                    );
//                                }
//                            )
//                            ->numeric(),
//                        DatePicker::make('payment_due_date')
//                            ->label(__('app.trade_docs.edit.payment_due_date'))
//                            ->native(false)
//                            ->displayFormat('Y-m-d')
//                            ->default(
//                                fn(Forms\Get $get) => Carbon::make($get('issued_at'))
//                                    ?->addDays($get('payment_credit_days'))
//                                    ->format('Y-m-d')
//                            )
//                            ->required(),
//                        Forms\Components\Checkbox::make('is_paid')
//                            ->label(__('app.trade_docs.edit.is_paid'))
//                            ->live()
//                            ->visible(fn($record) => $record->is_paid === true)
//                            ->inlineLabel(false)
//                            ->required(),
//                        Forms\Components\Actions::make([
//                            self::getFormPaymentAction()->hiddenLabel(false)
//                        ])
//                            ->visible(fn($record) => $record->is_paid === false),
//                        DatePicker::make('payment_date')
//                            ->label(__('app.trade_docs.edit.payment_date'))
//                            ->disabled(fn(Forms\Get $get) => !$get('is_paid'))
//                            ->native(false)
//                            ->displayFormat('Y-m-d'),
//
//                    ]),
//            ]);
//    }

    public static function createFormWithItems(Form $form): Form
    {
        return $form
            ->schema([
                self::docTypeSection(),
                ...self::baseSchema($form),
                self::itemsSection()
            ])
            ->inlineLabel(true)
            ->columns([
                'sm' => 1,
                'md' => 2,
                'lg' => 3,
            ])
            ->live();
    }

    public static function itemsSection(): Forms\Components\Section
    {
        return Forms\Components\Section::make(__('app.trade_docs.add_item.items_section_title'))
            ->description('Dodaj pozycje do faktury')
            ->columnSpanFull()
            ->schema([
                Forms\Components\Repeater::make('items')
                    ->label('')
                    ->schema([
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\Textarea::make('label')
                                    ->label(__('app.trade_docs.add_item.label'))
                                    ->required()
                                    ->columnSpan(3)
                                    ->maxLength(512),
                            ]),
                        Forms\Components\Grid::make(4)
                            ->schema([
                                Forms\Components\TextInput::make('net_unit_price')
                                    ->label(__('app.trade_docs.add_item.net_unit_price'))
                                    ->numeric()
                                    ->default(0.0)
                                    ->step(0.01)
                                    ->inputMode('decimal')
                                    ->stripCharacters(' ')
                                    ->live(onBlur: true)
                                    ->visible(function (Forms\Get $get) {
                                        $vatMethod = $get('../../vat_method');
                                        return $vatMethod == TradeDocVatMethod::BASE_ON_NET->value;
                                    })
                                    ->dehydratedWhenHidden(true)
                                    ->required()
                                    ->extraAttributes(function (Forms\Get $get) {
                                        $value = (float) $get('net_unit_price');
                                        return $value <= 0 ?
                                            ['style' => 'border-color: #ef4444 !important; border-width: 1px;'] :
                                            [];
                                    })
                                    ->afterStateUpdated(function (Forms\Set $set, Forms\Get $get, $state, $livewire) {
                                        if ($state === '0') {
                                            return;
                                        }
                                        self::calculateItemValuesWithVatMethod($set, $get, $state, 'net_unit_price', $livewire);
                                    }),
                                Forms\Components\TextInput::make('gross_unit_price')
                                    ->label(__('app.trade_docs.add_item.gross_unit_price'))
                                    ->numeric()
                                    ->default(0.0)
                                    ->step(0.01)
                                    ->inputMode('decimal')
                                    ->stripCharacters(' ')
                                    ->live(onBlur: true)
                                    ->visible(function (Forms\Get $get) {
                                        $vatMethod = $get('../../vat_method');
                                        return $vatMethod == TradeDocVatMethod::BASE_ON_GROSS->value;
                                    })
                                    ->dehydratedWhenHidden(true)
                                    ->required()
                                    ->extraAttributes(function (Forms\Get $get) {
                                        $value = (float) $get('gross_unit_price');
                                        return $value <= 0 ?
                                            ['style' => 'border-color: #ef4444 !important; border-width: 1px;'] :
                                            [];
                                    })
                                    ->afterStateUpdated(function (Forms\Set $set, Forms\Get $get, $state, Forms\Components\Component $component, $livewire) {
                                        if (blank($state)) {
                                            $component->state(0);
                                        }
                                        self::calculateItemValuesWithVatMethod($set, $get, $state, 'gross_unit_price', $livewire);
                                    }),
                                Forms\Components\TextInput::make('amount')
                                    ->label(__('app.trade_docs.add_item.amount'))
                                    ->numeric()
                                    ->default(1)
                                    ->step(0.01)
                                    ->minValue(0.01)
                                    ->required()
                                    ->live(onBlur: true)
                                    ->afterStateUpdated(function (Forms\Set $set, Forms\Get $get, $state, Forms\Components\Component $component, $livewire) {
                                        if ($state === null || $state === '') {
                                            $component->state(1);
                                            $state = 1;
                                        }
                                        self::calculateItemValuesWithVatMethod($set, $get, $state, 'amount', $livewire);
                                    }),
                                Forms\Components\Select::make('vat_label')
                                    ->label(__('app.trade_docs.add_item.vat_label'))
                                    ->options(fn() => \App\Enums\VatRates::getRatesForSelect())
                                    ->default('23')
                                    ->selectablePlaceholder(false)
                                    ->live()
                                    ->afterStateUpdated(function (Forms\Set $set, Forms\Get $get, $state, $livewire) {
                                        $vatRate = \App\Enums\VatRates::getRate($state);
                                        $set('vat_rate', $vatRate);
                                        self::calculateItemValuesWithVatMethod($set, $get, $state, 'vat_label', $livewire);
                                    }),
                                Forms\Components\TextInput::make('unit_type')
                                    ->label(__('app.trade_docs.add_item.unit_type'))
                                    ->default('szt.')
                                    ->maxLength(255),
                            ]),
                        Forms\Components\Grid::make(4)
                            ->schema([
                                Forms\Components\TextInput::make('net_value')
                                    ->label(__('app.trade_docs.add_item.net_value'))
                                    ->numeric()
                                    ->disabled()
                                    ->dehydrated(true)
                                    ->default(0.0),
                                Forms\Components\TextInput::make('vat_value')
                                    ->label(__('app.trade_docs.add_item.vat_value'))
                                    ->numeric()
                                    ->disabled()
                                    ->dehydrated(true)
                                    ->default(0.0),
                                Forms\Components\TextInput::make('gross_value')
                                    ->label(__('app.trade_docs.add_item.gross_value'))
                                    ->numeric()
                                    ->disabled()
                                    ->dehydrated(true)
                                    ->default(0.0),
                                Forms\Components\Hidden::make('vat_rate')
                                    ->default(23),
                            ]),
                    ])
                    ->addActionLabel('Dodaj pozycję')
                    ->reorderableWithButtons()
                    ->collapsible()
                    ->itemLabel(fn (array $state): ?string => $state['label'] ?? 'Nowa pozycja')
                    ->defaultItems(1)
                    ->minItems(0)
                    ->maxItems(50)
            ]);
    }

    protected static function calculateItemValues(Forms\Set $set, Forms\Get $get, $state, string $changedField): void
    {
        $netUnitPrice = (float) ($get('net_unit_price') ?? 0);
        $amount = (float) ($get('amount') ?? 1);
        $vatRate = (int) ($get('vat_rate') ?? 23);

        // Calculate net value
        $netValue = $netUnitPrice * $amount;

        // Calculate VAT value
        $vatValue = $netValue * ($vatRate / 100);

        // Calculate gross value
        $grossValue = $netValue + $vatValue;

        // Set calculated values
        $set('net_value', round($netValue, 2));
        $set('vat_value', round($vatValue, 2));
        $set('gross_value', round($grossValue, 2));
    }

    protected static function calculateItemValuesWithVatMethod(Forms\Set $set, Forms\Get $get, $state, string $changedField, $livewire = null): void
    {
        // Get VAT method from the Livewire component's form data
        $vatMethod = 1; // Default to BASE_ON_NET
        if ($livewire && method_exists($livewire, 'getFormData')) {
            $formData = $livewire->getFormData();
            $vatMethod = $formData['vat_method'] ?? 1;
        } else {
            // Fallback: try different paths to get VAT method
            $vatMethod = $get('../../vat_method') ?? $get('../../../vat_method') ?? $get('../../../../vat_method') ?? 1;
        }

        // Get current form values
        $netUnitPrice = (float) ($get('net_unit_price') ?? 0);
        $grossUnitPrice = (float) ($get('gross_unit_price') ?? 0);
        $amount = (float) ($get('amount') ?? 1);
        $vatRate = (int) ($get('vat_rate') ?? 23);

        // When user changes a field, update the appropriate value
        switch ($changedField) {
            case 'net_unit_price':
                $netUnitPrice = \Illuminate\Support\Str::of($state)->replace(',', '.')->toFloat();
                break;
            case 'gross_unit_price':
                $grossUnitPrice = \Illuminate\Support\Str::of($state)->replace(',', '.')->toFloat();
                break;
            case 'amount':
                $amount = (float) $state;
                break;
            case 'vat_label':
                // VAT rate is already updated via the vat_rate field
                break;
        }

        // Build docItem array with current values
        $docItem = [
            'net_unit_price' => $netUnitPrice,
            'gross_unit_price' => $grossUnitPrice,
            'amount' => $amount,
            'vat_rate' => $vatRate,
            'discount_type' => 0,
            'discount_value' => 0,
        ];

        // Use the CalculateTradeDoc service exactly like the trait does
        $calculator = \App\Services\CalculateTradeDoc::make($docItem);
        $calculator->setVatMethod(\App\Enums\TradeDocVatMethod::from($vatMethod ?? 1));
        $calculator->calculate();

        $results = $calculator->getResults();

        // Debug: Let's see what we're calculating
        if ($changedField === 'net_unit_price' && $netUnitPrice == 1000) {
            dd([
                'vatMethod' => $vatMethod,
                'vatMethodEnum' => \App\Enums\TradeDocVatMethod::from($vatMethod),
                'input' => $docItem,
                'results' => $results,
                'livewire_data' => $livewire ? (method_exists($livewire, 'getFormData') ? $livewire->getFormData() : 'no getFormData method') : 'no livewire'
            ]);
        }

        // Format and set results exactly like the trait does
        self::formatResult('net_unit_price', $results['net_unit_price'], $set);
        self::formatResult('gross_unit_price', $results['gross_unit_price'], $set);
        self::formatResult('net_value', $results['net_value'], $set);
        self::formatResult('vat_value', $results['vat_value'], $set);
        self::formatResult('gross_value', $results['gross_value'], $set);
    }

    protected static function formatResult($fieldName, $result, Forms\Set $set): void
    {
        $set($fieldName, number_format($result, 2, '.', ''));
    }
}
