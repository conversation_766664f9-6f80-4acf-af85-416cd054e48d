FROM nginx:1.29.0-alpine

# Copy SSL certificates
COPY docker/nginx/tf2ssl /etc/ssl/tf2ssl

# Set permissions for the SSL directory and its contents
RUN chmod -R 644 /etc/ssl/tf2ssl && \
    chmod 755 /etc/ssl/tf2ssl && \
    chown -R root:root /etc/ssl/tf2ssl

# create dummy ssl for ca
RUN apk add --no-cache openssl && \
    mkdir -p /etc/nginx/ssl && \
    openssl req -x509 -newkey rsa:2048 -keyout /etc/nginx/ssl/dummy.key \
    -out /etc/nginx/ssl/dummy.pem -days 365 -nodes \
    -subj "/C=US/ST=State/L=City/O=Org/CN=dummy"

# Set permissions for the SSL directory and its contents
RUN chmod -R 644 /etc/nginx/ssl/ && \
    chmod 755 /etc/nginx/ssl && \
    chown -R root:root /etc/nginx/ssl

