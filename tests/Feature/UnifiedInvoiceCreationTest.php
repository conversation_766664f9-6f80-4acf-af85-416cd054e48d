<?php

namespace Tests\Feature;

use App\Enums\DocumentTypes;
use App\Enums\PaymentTypes;
use App\Enums\TradeDocVatMethod;
use App\Filament\App\Resources\TradeDocResource\Pages\CreateTradeDocWithItems;
use App\Models\DocumentSeries;
use App\Models\Partner;
use App\Models\Tenant;
use App\Models\TradeDoc;
use App\Models\TradeDocItem;
use App\Models\User;
use Livewire\Livewire;
use Tests\TestCase;

class UnifiedInvoiceCreationTest extends TestCase
{

    protected User $user;
    protected Tenant $tenant;
    protected Partner $buyer;
    protected DocumentSeries $documentSeries;

    protected function setUp(): void
    {
        parent::setUp();

        // Use existing tenant and user from the database
        $this->tenant = Tenant::first();
        if (!$this->tenant) {
            $this->markTestSkipped('No tenant found in database');
        }

        $this->user = User::where('tenant_id', $this->tenant->id)->first();
        if (!$this->user) {
            $this->markTestSkipped('No user found for tenant');
        }

        // Use existing buyer or create one if none exists
        $this->buyer = Partner::where('installation', $this->tenant->id)->first();
        if (!$this->buyer) {
            $this->buyer = Partner::create([
                'installation' => $this->tenant->id,
                'name' => 'Test Buyer ' . uniqid(),
                'vat_id' => '0987654321' . rand(100, 999),
                'address' => 'Buyer Address 456',
                'postcode' => '11-111',
                'city' => 'Buyer City',
            ]);
        }

        // Use existing document series
        $this->documentSeries = DocumentSeries::where('installation', $this->tenant->id)
            ->where('type', DocumentTypes::FVS)
            ->first();
        if (!$this->documentSeries) {
            $this->markTestSkipped('No document series found for FVS type');
        }

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        // Clean up any test data created during tests
        if (isset($this->buyer) && $this->buyer->exists) {
            // Delete any trade documents created for this buyer during testing
            $testTradeDocs = TradeDoc::where('buyer_id', $this->buyer->id)
                ->where('created_at', '>=', now()->subMinutes(5))
                ->get();

            foreach ($testTradeDocs as $doc) {
                $doc->items()->delete();
                $doc->meta()->delete();
                $doc->delete();
            }

            // Only delete buyer if it was created during this test (has Test Buyer in name)
            if (str_contains($this->buyer->name, 'Test Buyer')) {
                $this->buyer->delete();
            }
        }

        parent::tearDown();
    }

    /** @test */
    public function it_can_create_invoice_with_items_in_single_step()
    {
        $formData = [
            'type' => DocumentTypes::FVS->value,
            'document_series_id' => $this->documentSeries->id,
            'buyer_id' => $this->buyer->id,
            'issued_at' => now()->format('Y-m-d'),
            'sells_date' => now()->format('Y-m-d'),
            'payment_due_date' => now()->addDays(14)->format('Y-m-d'),
            'payment_type' => PaymentTypes::TRANSFER->value,
            'currency' => 'PLN',
            'exchange_rate' => 1.0,
            'vat_method' => TradeDocVatMethod::BASE_ON_NET->value,
            'buyerdata' => [
                'name' => $this->buyer->name,
                'address' => $this->buyer->address,
                'postcode' => $this->buyer->postcode,
                'city' => $this->buyer->city,
                'vat_id' => $this->buyer->vat_id,
            ],
            'meta' => [
                'bank_account' => 'Test Bank Account',
                'options' => [
                    'reverse_charge' => false,
                    'mpp' => false,
                    'different_seller' => false,
                ],
                'note' => 'Test invoice note',
            ],
            'items' => [
                [
                    'label' => 'Test Product 1',
                    'net_unit_price' => 100.00,
                    'amount' => 2,
                    'unit_type' => 'szt.',
                    'vat_rate' => 23,
                    'vat_label' => '23',
                    'net_value' => 200.00,
                    'vat_value' => 46.00,
                    'gross_value' => 246.00,
                ],
                [
                    'label' => 'Test Product 2',
                    'net_unit_price' => 50.00,
                    'amount' => 1,
                    'unit_type' => 'szt.',
                    'vat_rate' => 23,
                    'vat_label' => '23',
                    'net_value' => 50.00,
                    'vat_value' => 11.50,
                    'gross_value' => 61.50,
                ],
            ],
        ];

        $component = Livewire::test(CreateTradeDocWithItems::class)
            ->fillForm($formData)
            ->call('create');

        // Assert that the trade document was created
        $this->assertDatabaseHas('trade_docs', [
            'buyer_id' => $this->buyer->id,
            'type' => DocumentTypes::FVS->value,
            'currency' => 'PLN',
        ]);

        // Get the created trade document
        $tradeDoc = TradeDoc::where('buyer_id', $this->buyer->id)->first();
        $this->assertNotNull($tradeDoc);

        // Assert that items were created for this specific trade document
        $items = TradeDocItem::where('trade_doc_uuid', $tradeDoc->uuid)->get();
        $this->assertCount(2, $items);

        // Assert first item
        $firstItem = $items->where('label', 'Test Product 1')->first();
        $this->assertNotNull($firstItem);
        $this->assertEquals('100.00', $firstItem->net_unit_price);
        $this->assertEquals(2, $firstItem->amount);
        $this->assertEquals('200.00', $firstItem->net_value);

        // Assert second item
        $secondItem = $items->where('label', 'Test Product 2')->first();
        $this->assertNotNull($secondItem);
        $this->assertEquals('50.00', $secondItem->net_unit_price);
        $this->assertEquals(1, $secondItem->amount);
        $this->assertEquals('50.00', $secondItem->net_value);

        // Assert document totals are calculated correctly
        $tradeDoc->refresh();
        $this->assertEquals('250.00', $tradeDoc->net); // 200 + 50
        $this->assertEquals('57.50', $tradeDoc->vat_amount); // 46 + 11.50
        $this->assertEquals('307.50', $tradeDoc->gross); // 246 + 61.50
    }

    /** @test */
    public function it_validates_required_fields()
    {
        $component = Livewire::test(CreateTradeDocWithItems::class)
            ->fillForm([
                // Missing required fields
            ])
            ->call('create')
            ->assertHasFormErrors([
                'type',
                'document_series_id',
                'buyer_id',
                'issued_at',
                'payment_type',
            ]);
    }

    /** @test */
    public function it_can_create_invoice_without_items()
    {
        $formData = [
            'type' => DocumentTypes::FVS->value,
            'document_series_id' => $this->documentSeries->id,
            'buyer_id' => $this->buyer->id,
            'issued_at' => now()->format('Y-m-d'),
            'sells_date' => now()->format('Y-m-d'),
            'payment_due_date' => now()->addDays(14)->format('Y-m-d'),
            'payment_type' => PaymentTypes::TRANSFER->value,
            'currency' => 'PLN',
            'exchange_rate' => 1.0,
            'vat_method' => TradeDocVatMethod::BASE_ON_NET->value,
            'buyerdata' => [
                'name' => $this->buyer->name,
                'address' => $this->buyer->address,
                'postcode' => $this->buyer->postcode,
                'city' => $this->buyer->city,
                'vat_id' => $this->buyer->vat_id,
            ],
            'meta' => [
                'bank_account' => 'Test Bank Account',
                'options' => [
                    'reverse_charge' => false,
                    'mpp' => false,
                    'different_seller' => false,
                ],
            ],
            'items' => [], // No items
        ];

        $component = Livewire::test(CreateTradeDocWithItems::class)
            ->fillForm($formData)
            ->call('create');

        // Assert that the trade document was created
        $this->assertDatabaseHas('trade_docs', [
            'buyer_id' => $this->buyer->id,
            'type' => DocumentTypes::FVS->value,
        ]);

        // Assert that no items were created
        $tradeDoc = TradeDoc::where('buyer_id', $this->buyer->id)->first();
        $this->assertCount(0, $tradeDoc->items);
    }
}
